<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\WalletTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Inertia\Inertia;

class WalletController extends Controller
{
    /**
     * Show the add funds page.
     */
    public function addFunds()
    {
        return Inertia::render('Wallet/AddFunds', [
            'user' => Auth::user(),
            'paystack_public_key' => config('services.paystack.public_key'),
        ]);
    }

    /**
     * Initialize Paystack payment.
     */
    public function initializePayment(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:10|max:50000',
        ]);

        $user = Auth::user();
        $amount = $request->amount * 100; // Convert to kobo (Paystack uses kobo)
        $reference = 'wallet_'.Str::random(10).'_'.time();

        // Create transaction record
        $transaction = WalletTransaction::create([
            'user_id' => $user->id,
            'transaction_id' => $reference,
            'type' => 'deposit',
            'amount' => $request->amount,
            'balance_before' => $user->wallet_balance,
            'balance_after' => $user->wallet_balance, // Will be updated on success
            'status' => 'pending',
            'payment_method' => 'paystack',
            'description' => 'Wallet top-up via Paystack',
        ]);

        // Initialize Paystack payment
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.config('services.paystack.secret_key'),
            'Content-Type' => 'application/json',
        ])->post('https://api.paystack.co/transaction/initialize', [
            'email' => $user->email,
            'amount' => $amount,
            'reference' => $reference,
            'callback_url' => route('wallet.verify'),
            'metadata' => [
                'user_id' => $user->id,
                'transaction_id' => $transaction->id,
            ],
        ]);

        if ($response->successful()) {
            $data = $response->json();

            // Update transaction with Paystack reference
            $transaction->update([
                'payment_reference' => $data['data']['reference'],
                'metadata' => $data['data'],
            ]);

            return response()->json([
                'status' => 'success',
                'authorization_url' => $data['data']['authorization_url'],
                'reference' => $reference,
            ]);
        }

        // Payment initialization failed
        $transaction->update(['status' => 'failed']);

        return response()->json([
            'status' => 'error',
            'message' => 'Failed to initialize payment. Please try again.',
        ], 400);
    }

    /**
     * Verify Paystack payment.
     */
    public function verifyPayment(Request $request)
    {
        $reference = $request->get('reference');

        if (! $reference) {
            return redirect()->route('wallet.add-funds')
                ->with('error', 'Invalid payment reference.');
        }

        // Verify payment with Paystack
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.config('services.paystack.secret_key'),
        ])->get("https://api.paystack.co/transaction/verify/{$reference}");

        if (! $response->successful()) {
            return redirect()->route('wallet.add-funds')
                ->with('error', 'Payment verification failed.');
        }

        $data = $response->json();

        if ($data['status'] && $data['data']['status'] === 'success') {
            $transaction = WalletTransaction::where('payment_reference', $reference)->first();

            if ($transaction && $transaction->status === 'pending') {
                DB::transaction(function () use ($transaction, $data) {
                    $user = $transaction->user;
                    $amount = $data['data']['amount'] / 100; // Convert from kobo

                    // Update user wallet balance
                    $newBalance = $user->wallet_balance + $amount;
                    $user->update(['wallet_balance' => $newBalance]);

                    // Update transaction
                    $transaction->update([
                        'status' => 'completed',
                        'balance_after' => $newBalance,
                        'metadata' => $data['data'],
                    ]);
                });

                return redirect()->route('wallet.add-funds')
                    ->with('success', 'Payment successful! Your wallet has been credited.');
            }
        }

        return redirect()->route('wallet.add-funds')
            ->with('error', 'Payment verification failed or already processed.');
    }

    /**
     * Show wallet transactions.
     */
    public function transactions()
    {
        $transactions = Auth::user()
            ->walletTransactions()
            ->latest()
            ->paginate(20);

        return Inertia::render('Wallet/Transactions', [
            'transactions' => $transactions,
        ]);
    }
}
