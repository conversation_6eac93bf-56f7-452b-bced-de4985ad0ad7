[2025-08-24 04:16:29] local.ERROR: [vite] Failed to reload /resources/js/layouts/auth-layout.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-24T04:16:28.360Z"} 
[2025-08-24 04:16:30] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-24T04:16:29.831Z"} 
[2025-08-24 04:16:51] local.ERROR: [vite] Failed to reload /resources/js/layouts/auth-layout.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-24T04:16:50.874Z"} 
[2025-08-29 12:32:28] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:32:22.635Z"} 
[2025-08-29 12:36:54] local.ERROR: [vite] Failed to reload /resources/js/pages/settings/password.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:36:47.573Z"} 
[2025-08-29 12:36:54] local.ERROR: [vite] Failed to reload /resources/js/pages/settings/appearance.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:36:47.573Z"} 
[2025-08-29 12:36:54] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:36:47.573Z"} 
[2025-08-29 12:36:54] local.ERROR: [vite] Failed to reload /resources/js/pages/settings/profile.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:36:47.573Z"} 
[2025-08-29 12:37:04] local.ERROR: Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:37:03.474Z"} 
[2025-08-29 12:37:04] local.ERROR: Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:37:03.482Z"} 
[2025-08-29 12:37:04] local.ERROR: Uncaught TypeError: Cannot read properties of null (reading 'useState') http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=b234a440 930 35 TypeError Cannot read properties of null (reading 'useState') TypeError: Cannot read properties of null (reading 'useState')
    at exports.useState (http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=b234a440:930:35)
    at NavigationMenu (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-navigation-menu.js?v=b1f86e35:75:55)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:4206:24)
    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:6461:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:7864:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10711:13) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:37:03.483Z"} 
[2025-08-29 12:37:04] local.ERROR: Uncaught TypeError: Cannot read properties of null (reading 'useState') http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=b234a440 930 35 TypeError Cannot read properties of null (reading 'useState') TypeError: Cannot read properties of null (reading 'useState')
    at exports.useState (http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=b234a440:930:35)
    at NavigationMenu (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-navigation-menu.js?v=b1f86e35:75:55)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:4206:24)
    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:6461:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:7864:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10711:13) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:37:03.484Z"} 
[2025-08-29 12:37:04] local.WARNING: %s

%s An error occurred in the <NavigationMenu> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:37:03.484Z"} 
[2025-08-29 13:13:03] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T13:06:12.335Z"} 
[2025-08-29 14:02:26] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T13:19:21.068Z"} 
[2025-08-29 18:02:07] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:06.095Z"} 
[2025-08-29 18:02:56] local.ERROR: Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem. {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:55.447Z"} 
[2025-08-29 18:02:56] local.ERROR: Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem. {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:55.479Z"} 
[2025-08-29 18:02:56] local.ERROR: Uncaught TypeError: Cannot read properties of null (reading 'useMemo') http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=6c62076f 918 35 TypeError Cannot read properties of null (reading 'useMemo') TypeError: Cannot read properties of null (reading 'useMemo')
    at exports.useMemo (http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=6c62076f:918:35)
    at useScope (http://[::1]:5173/node_modules/.vite/deps/chunk-RHOHTIHH.js?v=6c62076f:58:20)
    at Select (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=1d933fd6:104:23)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:10728:43) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:55.484Z"} 
[2025-08-29 18:02:56] local.ERROR: Uncaught TypeError: Cannot read properties of null (reading 'useMemo') http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=6c62076f 918 35 TypeError Cannot read properties of null (reading 'useMemo') TypeError: Cannot read properties of null (reading 'useMemo')
    at exports.useMemo (http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=6c62076f:918:35)
    at useScope (http://[::1]:5173/node_modules/.vite/deps/chunk-RHOHTIHH.js?v=6c62076f:58:20)
    at Select (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=1d933fd6:104:23)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:10728:43) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:55.485Z"} 
[2025-08-29 18:02:56] local.WARNING: %s

%s An error occurred in the <Select> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:55.485Z"} 
[2025-08-29 18:02:57] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/dashboard.tsx TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/dashboard.tsx {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:57.291Z"} 
[2025-08-29 18:05:06] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:06.123Z"} 
[2025-08-29 18:05:06] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:06.124Z"} 
[2025-08-29 18:05:06] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:06.125Z"} 
[2025-08-29 18:05:19] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:18.794Z"} 
[2025-08-29 18:05:19] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:18.794Z"} 
[2025-08-29 18:05:19] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:18.795Z"} 
[2025-08-29 18:05:42] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:41.959Z"} 
[2025-08-29 18:05:42] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:41.959Z"} 
[2025-08-29 18:05:42] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:41.960Z"} 
[2025-08-29 18:05:51] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:50.262Z"} 
[2025-08-29 18:05:51] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:50.262Z"} 
[2025-08-29 18:05:51] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:50.263Z"} 
[2025-08-29 18:06:38] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:06:37.443Z"} 
[2025-08-29 18:06:38] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:06:37.444Z"} 
[2025-08-29 18:06:38] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:06:37.445Z"} 
[2025-08-29 18:10:05] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:10:04.661Z"} 
[2025-08-29 18:10:21] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:10:21.375Z"} 
[2025-08-29 18:10:21] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:10:21.375Z"} 
[2025-08-29 18:10:21] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:10:21.376Z"} 
[2025-08-29 18:17:10] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:09.713Z"} 
[2025-08-29 18:17:10] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:09.715Z"} 
[2025-08-29 18:17:10] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:09.716Z"} 
[2025-08-29 18:17:23] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:22.199Z"} 
[2025-08-29 18:17:23] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:22.199Z"} 
[2025-08-29 18:17:23] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:22.200Z"} 
[2025-08-29 18:17:34] local.ERROR: Unhandled Promise Rejection ReferenceError Toaster is not defined ReferenceError: Toaster is not defined
    at setup (http://[::1]:5173/resources/js/app.tsx:20:32)
    at http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=6c62076f:14357:12
    at async createInertiaApp (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=6c62076f:14352:20) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:33.620Z"} 
[2025-08-29 18:19:10] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:19:09.870Z"} 
[2025-08-29 18:41:41] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:40.752Z"} 
[2025-08-29 18:41:41] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:40.752Z"} 
[2025-08-29 18:41:41] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:40.753Z"} 
[2025-08-29 18:41:48] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:47.547Z"} 
[2025-08-29 18:41:48] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:47.547Z"} 
[2025-08-29 18:41:48] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:47.547Z"} 
[2025-08-29 18:42:12] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:42:11.711Z"} 
[2025-08-29 18:42:12] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:42:11.711Z"} 
[2025-08-29 18:42:12] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:42:11.713Z"} 
[2025-08-29 18:45:43] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:45:43.328Z"} 
[2025-08-29 18:45:43] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:45:43.328Z"} 
[2025-08-29 18:45:43] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:45:43.329Z"} 
[2025-08-29 18:45:58] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:45:56.945Z"} 
[2025-08-29 18:46:42] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:46:41.905Z"} 
[2025-08-29 18:46:42] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:46:41.905Z"} 
[2025-08-29 18:46:42] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:46:41.906Z"} 
[2025-08-29 18:46:53] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:46:53.120Z"} 
[2025-08-29 18:47:23] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:47:23.236Z"} 
[2025-08-29 18:47:23] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:47:23.237Z"} 
[2025-08-29 18:47:23] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:47:23.237Z"} 
[2025-08-29 18:50:24] local.ERROR: Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb 871 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
    at SelectItem (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb:871:13)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6461:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7864:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:23.772Z"} 
[2025-08-29 18:50:24] local.ERROR: Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb 871 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
    at SelectItem (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb:871:13)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6461:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7864:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:23.773Z"} 
[2025-08-29 18:50:24] local.WARNING: %s

%s An error occurred in the <SelectItem> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:23.774Z"} 
[2025-08-29 18:50:57] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5174/resources/js/pages/projects/show.tsx 64 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5174/resources/js/pages/projects/show.tsx:64:37)
    at ShowProject (http://[::1]:5174/resources/js/pages/projects/show.tsx:159:83)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:56.763Z"} 
[2025-08-29 18:50:57] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5174/resources/js/pages/projects/show.tsx 64 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5174/resources/js/pages/projects/show.tsx:64:37)
    at ShowProject (http://[::1]:5174/resources/js/pages/projects/show.tsx:159:83)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:56.763Z"} 
[2025-08-29 18:50:57] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:56.764Z"} 
[2025-08-29 18:51:16] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/projects/edit.tsx Error: Page not found: ./pages/projects/edit.tsx
    at resolvePageComponent (http://[::1]:5174/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=0ee6d8bb:12:9)
    at resolve (http://[::1]:5174/resources/js/app.tsx:11:22)
    at CurrentPage.resolveComponent (http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:14350:54)
    at CurrentPage.resolve (http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:12072:33)
    at CurrentPage.set (http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:12002:17)
    at _Response.setPage (http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:13053:17)
    at async _Response.process (http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:12980:5) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:51:15.619Z"} 
[2025-08-29 18:51:26] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5174/resources/js/pages/projects/show.tsx 64 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5174/resources/js/pages/projects/show.tsx:64:37)
    at ShowProject (http://[::1]:5174/resources/js/pages/projects/show.tsx:159:83)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:51:26.339Z"} 
[2025-08-29 18:51:26] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5174/resources/js/pages/projects/show.tsx 64 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5174/resources/js/pages/projects/show.tsx:64:37)
    at ShowProject (http://[::1]:5174/resources/js/pages/projects/show.tsx:159:83)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:51:26.339Z"} 
[2025-08-29 18:51:26] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:51:26.339Z"} 
[2025-08-29 18:52:05] local.ERROR: Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5174/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb 871 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
    at SelectItem (http://[::1]:5174/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb:871:13)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateForwardRef (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6461:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7864:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:52:04.995Z"} 
[2025-08-29 18:52:05] local.ERROR: Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5174/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb 871 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
    at SelectItem (http://[::1]:5174/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb:871:13)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateForwardRef (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6461:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7864:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:52:04.995Z"} 
[2025-08-29 18:52:05] local.WARNING: %s

%s An error occurred in the <SelectItem> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:52:04.996Z"} 
[2025-08-29 19:14:37] local.ERROR: [vite] Failed to reload /resources/js/components/project-card.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:14:36.766Z"} 
[2025-08-29 19:14:51] local.ERROR: [vite] Failed to reload /resources/js/components/project-card.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:14:50.786Z"} 
[2025-08-29 19:15:03] local.ERROR: [vite] Failed to reload /resources/js/components/project-card.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:15:02.100Z"} 
[2025-08-29 19:31:19] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:31:18.919Z"} 
[2025-08-29 19:31:40] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:31:39.019Z"} 
[2025-08-29 19:37:28] local.ERROR: [vite] Failed to reload /resources/js/components/google-button.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:37:27.805Z"} 
[2025-08-29 19:40:33] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:40:33.461Z"} 
[2025-08-29 19:40:48] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:40:47.921Z"} 
[2025-08-29 19:48:02] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:48:00.655Z"} 
[2025-08-29 19:48:13] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:48:12.032Z"} 
[2025-08-29 19:48:16] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:48:15.421Z"} 
[2025-08-29 19:48:48] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:48:47.437Z"} 
[2025-08-29 19:49:02] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:49:02.200Z"} 
[2025-08-29 19:50:54] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:50:53.806Z"} 
[2025-08-29 19:52:21] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:52:21.721Z"} 
[2025-08-29 19:54:05] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:54:04.943Z"} 
[2025-08-29 19:57:13] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:54:51.410Z"} 
[2025-08-30 11:49:08] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T11:49:07.922Z"} 
[2025-08-30 12:01:26] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T12:01:25.905Z"} 
[2025-08-30 12:03:54] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login?id=614f0b2d-175d-4270-9641-f123e0c2253f&vscodeBrowserReqId=1756555397049","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.103.2 Chrome/138.0.7204.100 Electron/37.2.3 Safari/537.36","timestamp":"2025-08-30T12:03:53.381Z"} 
[2025-08-30 12:03:54] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T12:03:53.412Z"} 
[2025-08-30 12:05:36] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login?id=614f0b2d-175d-4270-9641-f123e0c2253f&vscodeBrowserReqId=1756555517096","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.103.2 Chrome/138.0.7204.100 Electron/37.2.3 Safari/537.36","timestamp":"2025-08-30T12:05:35.423Z"} 
[2025-08-30 12:05:37] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T12:05:35.546Z"} 
[2025-08-30 12:36:14] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T12:36:13.224Z"} 
[2025-08-30 12:43:44] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T12:43:43.015Z"} 
[2025-08-30 13:11:28] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:27.973Z"} 
[2025-08-30 13:11:28] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:27.973Z"} 
[2025-08-30 13:11:28] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:27.974Z"} 
[2025-08-30 13:11:38] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:37.704Z"} 
[2025-08-30 13:11:38] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:37.704Z"} 
[2025-08-30 13:11:38] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:37.704Z"} 
[2025-08-30 13:11:47] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:46.986Z"} 
[2025-08-30 13:11:47] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:46.986Z"} 
[2025-08-30 13:11:47] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:46.986Z"} 
[2025-08-30 13:12:42] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:12:42.089Z"} 
[2025-08-30 13:12:42] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:12:42.089Z"} 
[2025-08-30 13:12:42] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:12:42.090Z"} 
[2025-08-30 13:15:44] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:15:44.414Z"} 
[2025-08-30 13:15:44] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:15:44.414Z"} 
[2025-08-30 13:15:44] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:15:44.416Z"} 
[2025-08-30 13:21:16] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:21:16.436Z"} 
[2025-08-30 13:21:16] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:21:16.436Z"} 
[2025-08-30 13:21:16] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:21:16.440Z"} 
[2025-08-30 13:48:16] local.DEBUG: Project data: null {"url":"http://localhost:8000/projects/e-commerce-website-development-JvEEUI","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:48:15.783Z"} 
[2025-08-30 13:58:46] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/projects/e-commerce-website-development-JvEEUI","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:58:45.725Z"} 
[2025-08-30 13:58:46] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:58:45.726Z"} 
[2025-08-30 14:01:32] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:31.508Z"} 
[2025-08-30 14:01:32] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:31.508Z"} 
[2025-08-30 14:01:32] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:31.511Z"} 
[2025-08-30 14:01:41] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:40.521Z"} 
[2025-08-30 14:01:41] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:40.521Z"} 
[2025-08-30 14:01:41] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:40.522Z"} 
[2025-08-30 14:18:20] local.ERROR: Uncaught ReferenceError: Link is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500 811 15 ReferenceError Link is not defined ReferenceError: Link is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500:811:15)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:18:19.744Z"} 
[2025-08-30 14:18:20] local.ERROR: Uncaught ReferenceError: Link is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500 811 15 ReferenceError Link is not defined ReferenceError: Link is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500:811:15)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:18:19.744Z"} 
[2025-08-30 14:18:20] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:18:19.747Z"} 
[2025-08-30 14:18:55] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:18:54.664Z"} 
[2025-08-30 14:21:38] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563535210 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563535210 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:37.962Z"} 
[2025-08-30 14:21:40] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563699022 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563699022 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:40.142Z"} 
[2025-08-30 14:21:42] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563700874 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563700874 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:42.122Z"} 
[2025-08-30 14:21:44] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563702904 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563702904 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:44.195Z"} 
[2025-08-30 14:21:46] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563705010 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563705010 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:45.744Z"} 
[2025-08-30 14:21:48] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563706578 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563706578 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:47.669Z"} 
[2025-08-30 14:21:50] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563708455 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563708455 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:49.739Z"} 
[2025-08-30 14:21:52] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563710601 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563710601 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:51.813Z"} 
[2025-08-30 14:21:54] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563712572 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563712572 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:53.673Z"} 
[2025-08-30 14:21:56] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563714486 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563714486 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:55.580Z"} 
[2025-08-30 14:21:58] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563716549 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563716549 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:57.508Z"} 
[2025-08-30 14:22:00] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563718329 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563718329 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:59.615Z"} 
[2025-08-30 14:22:02] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563720655 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563720655 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:01.350Z"} 
[2025-08-30 14:22:04] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563722231 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563722231 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:03.262Z"} 
[2025-08-30 14:22:06] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563724068 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563724068 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:05.486Z"} 
[2025-08-30 14:22:09] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563726153 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563726153 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:08.296Z"} 
[2025-08-30 14:22:13] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563729743 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563729743 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:12.199Z"} 
[2025-08-30 14:22:40] local.ERROR: Uncaught TypeError: project.accepted_bid_amount.toFixed is not a function http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563732447 264 49 TypeError project.accepted_bid_amount.toFixed is not a function TypeError: project.accepted_bid_amount.toFixed is not a function
    at Milestones (http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563732447:264:49)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10359:46) {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:40.370Z"} 
[2025-08-30 14:22:40] local.ERROR: Uncaught TypeError: project.accepted_bid_amount.toFixed is not a function http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563732447 264 49 TypeError project.accepted_bid_amount.toFixed is not a function TypeError: project.accepted_bid_amount.toFixed is not a function
    at Milestones (http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563732447:264:49)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10359:46) {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:40.370Z"} 
[2025-08-30 14:22:40] local.WARNING: %s

%s An error occurred in the <Milestones> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:40.373Z"} 
[2025-08-30 15:26:31] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:31.284Z"} 
[2025-08-30 15:26:31] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:31.285Z"} 
[2025-08-30 15:26:31] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:31.285Z"} 
[2025-08-30 15:26:35] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:35.616Z"} 
[2025-08-30 15:26:35] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:35.616Z"} 
[2025-08-30 15:26:35] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:35.617Z"} 
