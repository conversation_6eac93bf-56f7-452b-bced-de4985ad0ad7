<?php

namespace App\Http\Controllers;

use App\Models\Bid;
use App\Models\Project;
use App\Services\EscrowService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BidController extends Controller
{
    public function store(Request $request, Project $project)
    {
        // Prevent bidding on own projects
        if ($project->user_id === Auth::id()) {
            return back()->withErrors(['error' => 'You cannot bid on your own project.']);
        }

        // Prevent bidding on closed projects
        if ($project->status !== 'open') {
            return back()->withErrors(['error' => 'This project is no longer accepting bids.']);
        }

        // Prevent project owners from bidding on their own projects
        if ($project->user_id === Auth::id()) {
            return back()->withErrors(['error' => 'You cannot bid on your own project.']);
        }

        $validated = $request->validate([
            'amount' => 'required|numeric|min:1',
            'proposal' => 'required|string|min:50',
            'delivery_days' => 'required|integer|min:1|max:365',
        ]);

        try {
            Bid::create([
                'project_id' => $project->id,
                'user_id' => Auth::id(),
                ...$validated,
            ]);

            return back()->with('success', 'Your bid has been submitted successfully!');
        } catch (\Illuminate\Database\QueryException $e) {
            // Handle duplicate bid constraint
            if ($e->getCode() === '23000') {
                return back()->withErrors(['error' => 'You have already submitted a bid for this project.']);
            }
            throw $e;
        }
    }

    /**
     * Accept a bid for a project.
     */
    public function accept(Project $project, Bid $bid)
    {
        // Ensure the bid belongs to the project
        if ($bid->project_id !== $project->id) {
            return back()->withErrors(['error' => 'Invalid bid for this project.']);
        }

        // Ensure the user owns the project
        if ($project->user_id !== Auth::id()) {
            return back()->withErrors(['error' => 'You can only accept bids on your own projects.']);
        }

        // Ensure the project is still open
        if ($project->status !== 'open') {
            return back()->withErrors(['error' => 'This project is no longer accepting bids.']);
        }

        // Ensure the bid is still pending
        if ($bid->status !== 'pending') {
            return back()->withErrors(['error' => 'This bid has already been processed.']);
        }

        try {
            DB::transaction(function () use ($project, $bid) {
                // Accept the bid
                $bid->update(['status' => 'accepted']);

                // Reject all other bids for this project
                Bid::where('project_id', $project->id)
                    ->where('id', '!=', $bid->id)
                    ->where('status', 'pending')
                    ->update(['status' => 'rejected']);

                // Update project with assigned freelancer
                $project->update([
                    'status' => 'in_progress',
                    'assigned_freelancer_id' => $bid->user_id,
                    'accepted_bid_amount' => $bid->amount,
                    'assigned_at' => now(),
                ]);

                // Create escrow for the project
                $escrowService = new EscrowService();
                $escrowCreated = $escrowService->createEscrow($project->fresh());

                if (!$escrowCreated) {
                    throw new \Exception('Failed to create escrow for the project');
                }
            });

            return back()->with('success', 'Bid accepted successfully! The project has been assigned to the freelancer and escrow has been created.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to accept bid. Please try again.']);
        }
    }

    /**
     * Reject a bid for a project.
     */
    public function reject(Project $project, Bid $bid)
    {
        // Ensure the bid belongs to the project
        if ($bid->project_id !== $project->id) {
            return back()->withErrors(['error' => 'Invalid bid for this project.']);
        }

        // Ensure the user owns the project
        if ($project->user_id !== Auth::id()) {
            return back()->withErrors(['error' => 'You can only reject bids on your own projects.']);
        }

        // Ensure the bid is still pending
        if ($bid->status !== 'pending') {
            return back()->withErrors(['error' => 'This bid has already been processed.']);
        }

        try {
            $bid->update(['status' => 'rejected']);
            return back()->with('success', 'Bid rejected successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to reject bid. Please try again.']);
        }
    }
}
