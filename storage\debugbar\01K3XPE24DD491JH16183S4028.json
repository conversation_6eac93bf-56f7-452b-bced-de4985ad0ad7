{"__meta": {"id": "01K3XPE24DD491JH16183S4028", "datetime": "2025-08-30 14:34:50", "utime": **********.382396, "method": "PATCH", "uri": "/projects/e-commerce-website-development-YEOOQW/milestones/1/approve", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": 1756564489.955915, "end": **********.382409, "duration": 0.42649412155151367, "duration_str": "426ms", "measures": [{"label": "Booting", "start": 1756564489.955915, "relative_start": 0, "end": **********.117314, "relative_end": **********.117314, "duration": 0.*****************, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.117332, "relative_start": 0.*****************, "end": **********.38241, "relative_end": 9.5367431640625e-07, "duration": 0.***************, "duration_str": "265ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.134032, "relative_start": 0.***************, "end": **********.136077, "relative_end": **********.136077, "duration": 0.0020449161529541016, "duration_str": "2.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.379179, "relative_start": 0.****************, "end": **********.3795, "relative_end": **********.3795, "duration": 0.0003209114074707031, "duration_str": "321μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 18, "nb_statements": 15, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.14830000000000002, "accumulated_duration_str": "148ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.146533, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = '1LBsvFMJGRN2ZAIEe15GqxZoERlK01D5cKhC2UBG' limit 1", "type": "query", "params": [], "bindings": ["1LBsvFMJGRN2ZAIEe15GqxZoERlK01D5cKhC2UBG"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.15066, "duration": 0.04951, "duration_str": "49.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 33.385}, {"sql": "select * from \"users\" where \"id\" = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.209671, "duration": 0.0075, "duration_str": "7.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 33.385, "width_percent": 5.057}, {"sql": "select * from \"projects\" where \"slug\" = 'e-commerce-website-development-YEOOQW' limit 1", "type": "query", "params": [], "bindings": ["e-commerce-website-development-YEOOQW"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 980}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.220516, "duration": 0.004900000000000001, "duration_str": "4.9ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "thesylink", "explain": null, "start_percent": 38.442, "width_percent": 3.304}, {"sql": "select * from \"project_milestones\" where \"id\" = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 980}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.226857, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "thesylink", "explain": null, "start_percent": 41.746, "width_percent": 3.129}, {"sql": "update \"project_milestones\" set \"status\" = 'approved', \"approved_at\" = '2025-08-30 14:34:50', \"approval_notes\" = 'Excellent work on Chapter 1! The project setup and database design look solid. The authentication system is well-implemented and the frontend structure is clean and professional. Approved for payment release.', \"updated_at\" = '2025-08-30 14:34:50' where \"id\" = 1", "type": "query", "params": [], "bindings": ["approved", "2025-08-30 14:34:50", "Excellent work on Chapter 1! The project setup and database design look solid. The authentication system is well-implemented and the frontend structure is clean and professional. Approved for payment release.", "2025-08-30 14:34:50", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 135}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.2571762, "duration": 0.02379, "duration_str": "23.79ms", "memory": 0, "memory_str": null, "filename": "MilestoneController.php:135", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FMilestoneController.php&line=135", "ajax": false, "filename": "MilestoneController.php", "line": "135"}, "connection": "thesylink", "explain": null, "start_percent": 44.875, "width_percent": 16.042}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.288365, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EscrowService.php:86", "source": {"index": 10, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=86", "ajax": false, "filename": "EscrowService.php", "line": "86"}, "connection": "thesylink", "explain": null, "start_percent": 60.917, "width_percent": 0}, {"sql": "select * from \"projects\" where \"projects\".\"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 87}, {"index": 25, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.291297, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:87", "source": {"index": 21, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=87", "ajax": false, "filename": "EscrowService.php", "line": "87"}, "connection": "thesylink", "explain": null, "start_percent": 60.917, "width_percent": 2.299}, {"sql": "insert into \"escrow_transactions\" (\"project_id\", \"milestone_id\", \"client_id\", \"freelancer_id\", \"transaction_id\", \"type\", \"amount\", \"commission_amount\", \"freelancer_amount\", \"status\", \"description\", \"processed_at\", \"updated_at\", \"created_at\") values (1, 1, 3, 6, 'escrow_release_**********_1_68b30c0a48cec', 'release', '148.50', 44.55, 103.95, 'completed', 'Payment release for Chapter 1', '2025-08-30 14:34:50', '2025-08-30 14:34:50', '2025-08-30 14:34:50') returning \"id\"", "type": "query", "params": [], "bindings": [1, 1, 3, 6, "escrow_release_**********_1_68b30c0a48cec", "release", "148.50", 44.55, 103.94999999999999, "completed", "Payment release for Chapter 1", "2025-08-30 14:34:50", "2025-08-30 14:34:50", "2025-08-30 14:34:50"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 92}, {"index": 26, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.299023, "duration": 0.01332, "duration_str": "13.32ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:92", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=92", "ajax": false, "filename": "EscrowService.php", "line": "92"}, "connection": "thesylink", "explain": null, "start_percent": 63.216, "width_percent": 8.982}, {"sql": "insert into \"platform_commissions\" (\"project_id\", \"milestone_id\", \"escrow_transaction_id\", \"amount\", \"commission_rate\", \"status\", \"collected_at\", \"updated_at\", \"created_at\") values (1, 1, 2, 44.55, 30, 'collected', '2025-08-30 14:34:50', '2025-08-30 14:34:50', '2025-08-30 14:34:50') returning \"id\"", "type": "query", "params": [], "bindings": [1, 1, 2, 44.55, 30, "collected", "2025-08-30 14:34:50", "2025-08-30 14:34:50", "2025-08-30 14:34:50"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 108}, {"index": 26, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.3150601, "duration": 0.01396, "duration_str": "13.96ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:108", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=108", "ajax": false, "filename": "EscrowService.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 72.198, "width_percent": 9.413}, {"sql": "select * from \"users\" where \"users\".\"id\" = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 119}, {"index": 24, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.329948, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:119", "source": {"index": 20, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=119", "ajax": false, "filename": "EscrowService.php", "line": "119"}, "connection": "thesylink", "explain": null, "start_percent": 81.612, "width_percent": 1.322}, {"sql": "update \"users\" set \"wallet_balance\" = \"wallet_balance\" + 103.95, \"updated_at\" = '2025-08-30 14:34:50' where \"id\" = 6", "type": "query", "params": [], "bindings": ["2025-08-30 14:34:50", 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 120}, {"index": 21, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.3339071, "duration": 0.009779999999999999, "duration_str": "9.78ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:120", "source": {"index": 17, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=120", "ajax": false, "filename": "EscrowService.php", "line": "120"}, "connection": "thesylink", "explain": null, "start_percent": 82.933, "width_percent": 6.595}, {"sql": "update \"project_milestones\" set \"status\" = 'paid', \"paid_at\" = '2025-08-30 14:34:50', \"updated_at\" = '2025-08-30 14:34:50' where \"id\" = 1", "type": "query", "params": [], "bindings": ["paid", "2025-08-30 14:34:50", "2025-08-30 14:34:50", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 123}, {"index": 19, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.3448868, "duration": 0.005, "duration_str": "5ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:123", "source": {"index": 15, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=123", "ajax": false, "filename": "EscrowService.php", "line": "123"}, "connection": "thesylink", "explain": null, "start_percent": 89.528, "width_percent": 3.372}, {"sql": "update \"projects\" set \"completed_milestones\" = \"completed_milestones\" + 1, \"updated_at\" = '2025-08-30 14:34:50' where \"id\" = 1", "type": "query", "params": [], "bindings": ["2025-08-30 14:34:50", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 129}, {"index": 21, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.351266, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:129", "source": {"index": 17, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=129", "ajax": false, "filename": "EscrowService.php", "line": "129"}, "connection": "thesylink", "explain": null, "start_percent": 92.9, "width_percent": 2.643}, {"sql": "update \"projects\" set \"total_released\" = \"total_released\" + 148.50, \"updated_at\" = '2025-08-30 14:34:50' where \"id\" = 1", "type": "query", "params": [], "bindings": ["2025-08-30 14:34:50", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 130}, {"index": 21, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.358413, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:130", "source": {"index": 17, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=130", "ajax": false, "filename": "EscrowService.php", "line": "130"}, "connection": "thesylink", "explain": null, "start_percent": 95.543, "width_percent": 1.746}, {"sql": "update \"projects\" set \"total_commission\" = \"total_commission\" + 44.55, \"updated_at\" = '2025-08-30 14:34:50' where \"id\" = 1", "type": "query", "params": [], "bindings": ["2025-08-30 14:34:50", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 131}, {"index": 21, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.3623111, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:131", "source": {"index": 17, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=131", "ajax": false, "filename": "EscrowService.php", "line": "131"}, "connection": "thesylink", "explain": null, "start_percent": 97.289, "width_percent": 1.416}, {"sql": "update \"projects\" set \"escrow_status\" = 'partially_released', \"updated_at\" = '2025-08-30 14:34:50' where \"id\" = 1", "type": "query", "params": [], "bindings": ["partially_released", "2025-08-30 14:34:50", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 137}, {"index": 19, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.365765, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:137", "source": {"index": 15, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=137", "ajax": false, "filename": "EscrowService.php", "line": "137"}, "connection": "thesylink", "explain": null, "start_percent": 98.705, "width_percent": 1.295}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/MilestoneController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\MilestoneController.php", "line": 142}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.373609, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EscrowService.php:86", "source": {"index": 9, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=86", "ajax": false, "filename": "EscrowService.php", "line": "86"}, "connection": "thesylink", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\Project": {"retrieved": 2, "updated": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 2, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProjectMilestone": {"retrieved": 1, "updated": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProjectMilestone.php&line=1", "ajax": false, "filename": "ProjectMilestone.php", "line": "?"}}, "App\\Models\\EscrowTransaction": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FEscrowTransaction.php&line=1", "ajax": false, "filename": "EscrowTransaction.php", "line": "?"}}, "App\\Models\\PlatformCommission": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FPlatformCommission.php&line=1", "ajax": false, "filename": "PlatformCommission.php", "line": "?"}}}, "count": 14, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 5, "updated": 7, "created": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones/1/approve", "action_name": "milestones.approve", "controller_action": "App\\Http\\Controllers\\MilestoneController@approve", "uri": "PATCH projects/{project}/milestones/{milestone}/approve", "controller": "App\\Http\\Controllers\\MilestoneController@approve<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FMilestoneController.php&line=112\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FMilestoneController.php&line=112\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/MilestoneController.php:112-154</a>", "middleware": "web, auth, verified", "duration": "428ms", "peak_memory": "26MB", "response": "Redirect to http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2000818339 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2000818339\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2131779204 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>submission_notes</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>approval_notes</span>\" => \"<span class=sf-dump-str title=\"208 characters\">Excellent work on Chapter 1! The project setup and database design look solid. The authentication system is well-implemented and the frontend structure is clean and professional. Approved for payment release.</span>\"\n  \"<span class=sf-dump-key>revision_notes</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131779204\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-920255844 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">271</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjFwcUFWK0RWVHcrNE1qZVBhQ2lJTmc9PSIsInZhbHVlIjoiMG9Sc3RIM3NWdjczWVJPQTgwZWpBbE5YZzhkdzUrTGcxZHdZdVRnanZBUmcrL1ZjR1M5R2VtU0NoTWxuQ1VuaklPRHZXZ3kyWE9wL1N1Kyt6dXZlQmhobWxQM3kwejdYNmZjMFJTaTdnNGd4eVBuTHF3dzFQVmVpV1UvaGNpQVUiLCJtYWMiOiI2NDcwOTVhYThkNjllNTE4ZTg4NDU2ZGI4NDM2YzEwNzRkOTAwYWFjYWEyZmE5MGNlMmUwN2NjNGFhMjZmZmMwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"79 characters\">http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"958 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6IjFwcUFWK0RWVHcrNE1qZVBhQ2lJTmc9PSIsInZhbHVlIjoiMG9Sc3RIM3NWdjczWVJPQTgwZWpBbE5YZzhkdzUrTGcxZHdZdVRnanZBUmcrL1ZjR1M5R2VtU0NoTWxuQ1VuaklPRHZXZ3kyWE9wL1N1Kyt6dXZlQmhobWxQM3kwejdYNmZjMFJTaTdnNGd4eVBuTHF3dzFQVmVpV1UvaGNpQVUiLCJtYWMiOiI2NDcwOTVhYThkNjllNTE4ZTg4NDU2ZGI4NDM2YzEwNzRkOTAwYWFjYWEyZmE5MGNlMmUwN2NjNGFhMjZmZmMwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjJNK3U5N1NPL3Z5dVNjZFM0Y2cwK1E9PSIsInZhbHVlIjoiN3J1S2Y4cHREQjk3ckJORGluamRtd3BadWdDNkgxWU9pbHVybEtYVnFwTVlSTjY1d0ZBRnpnQmRmMVdQdFdlVmE5N1FvaDdlVVg0RWt3cmxJK3ZiQ1paNGhWdFl5NVVSdUJ1d01BdzFqU2xqTGc2dEhMSXdkQ2dWMTV3bEhtWTciLCJtYWMiOiI0MGM0MjcwZWIxYzRiNDNhMmYwMmY2MWM3NDlkOTMyZWNiNTRmMTEyNWVjYmU1YjI2ZjQyNGViN2QyMTA4YjhlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920255844\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-850028541 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">01OxAJm9OpbFA90nzoy7uDuvC9OWmqYdHu2or5cI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LBsvFMJGRN2ZAIEe15GqxZoERlK01D5cKhC2UBG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850028541\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1155538480 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 30 Aug 2025 14:34:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"79 characters\">http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155538480\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-947373681 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">01OxAJm9OpbFA90nzoy7uDuvC9OWmqYdHu2or5cI</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K3XP7HRGZD2RT7QKXFAXJ82C</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>01K3XP8VKK321E0XMJDBEND521</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Milestone approved and payment released successfully.</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947373681\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones/1/approve", "action_name": "milestones.approve", "controller_action": "App\\Http\\Controllers\\MilestoneController@approve"}, "badge": "302 Found"}}