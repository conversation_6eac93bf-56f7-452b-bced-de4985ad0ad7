{"__meta": {"id": "01K3XR865MM1JN102Z35DR5HWH", "datetime": "2025-08-30 15:06:35", "utime": **********.061876, "method": "PATCH", "uri": "/projects/ai-chatbot-for-customer-service-uRET61/bids/19/accept", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.573695, "end": **********.061894, "duration": 0.48819899559020996, "duration_str": "488ms", "measures": [{"label": "Booting", "start": **********.573695, "relative_start": 0, "end": **********.77628, "relative_end": **********.77628, "duration": 0.*****************, "duration_str": "203ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.776291, "relative_start": 0.*****************, "end": **********.061896, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "286ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.791935, "relative_start": 0.*****************, "end": **********.793744, "relative_end": **********.793744, "duration": 0.0018091201782226562, "duration_str": "1.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.057344, "relative_start": 0.*****************, "end": **********.05765, "relative_end": **********.05765, "duration": 0.00030612945556640625, "duration_str": "306μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 23, "nb_statements": 18, "nb_visible_statements": 23, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.14288999999999996, "accumulated_duration_str": "143ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.821895, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = '1LBsvFMJGRN2ZAIEe15GqxZoERlK01D5cKhC2UBG' limit 1", "type": "query", "params": [], "bindings": ["1LBsvFMJGRN2ZAIEe15GqxZoERlK01D5cKhC2UBG"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.827488, "duration": 0.054799999999999995, "duration_str": "54.8ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 38.351}, {"sql": "select * from \"users\" where \"id\" = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.8996081, "duration": 0.00641, "duration_str": "6.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 38.351, "width_percent": 4.486}, {"sql": "select * from \"projects\" where \"slug\" = 'ai-chatbot-for-customer-service-uRET61' limit 1", "type": "query", "params": [], "bindings": ["ai-chatbot-for-customer-service-uRET61"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 980}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.911716, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "thesylink", "explain": null, "start_percent": 42.837, "width_percent": 2.813}, {"sql": "select * from \"bids\" where \"id\" = '19' limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 980}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.917062, "duration": 0.00478, "duration_str": "4.78ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "thesylink", "explain": null, "start_percent": 45.651, "width_percent": 3.345}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.930533, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BidController.php:80", "source": {"index": 10, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=80", "ajax": false, "filename": "BidController.php", "line": "80"}, "connection": "thesylink", "explain": null, "start_percent": 48.996, "width_percent": 0}, {"sql": "update \"bids\" set \"status\" = 'accepted', \"updated_at\" = '2025-08-30 15:06:34' where \"id\" = 19", "type": "query", "params": [], "bindings": ["accepted", "2025-08-30 15:06:34", 19], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 82}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.931123, "duration": 0.011519999999999999, "duration_str": "11.52ms", "memory": 0, "memory_str": null, "filename": "BidController.php:82", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=82", "ajax": false, "filename": "BidController.php", "line": "82"}, "connection": "thesylink", "explain": null, "start_percent": 48.996, "width_percent": 8.062}, {"sql": "update \"bids\" set \"status\" = 'rejected', \"updated_at\" = '2025-08-30 15:06:34' where \"project_id\" = 7 and \"id\" != 19 and \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["rejected", "2025-08-30 15:06:34", 7, 19, "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.946889, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "BidController.php:88", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=88", "ajax": false, "filename": "BidController.php", "line": "88"}, "connection": "thesylink", "explain": null, "start_percent": 57.058, "width_percent": 1.988}, {"sql": "update \"projects\" set \"status\" = 'in_progress', \"assigned_freelancer_id\" = 6, \"accepted_bid_amount\" = '1154.00', \"assigned_at\" = '2025-08-30 15:06:34', \"updated_at\" = '2025-08-30 15:06:34' where \"id\" = 7", "type": "query", "params": [], "bindings": ["in_progress", 6, "1154.00", "2025-08-30 15:06:34", "2025-08-30 15:06:34", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.954015, "duration": 0.0074800000000000005, "duration_str": "7.48ms", "memory": 0, "memory_str": null, "filename": "BidController.php:91", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=91", "ajax": false, "filename": "BidController.php", "line": "91"}, "connection": "thesylink", "explain": null, "start_percent": 59.045, "width_percent": 5.235}, {"sql": "select * from \"projects\" where \"id\" = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.963487, "duration": 0.00667, "duration_str": "6.67ms", "memory": 0, "memory_str": null, "filename": "BidController.php:100", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=100", "ajax": false, "filename": "BidController.php", "line": "100"}, "connection": "thesylink", "explain": null, "start_percent": 64.28, "width_percent": 4.668}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.974041, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EscrowService.php:23", "source": {"index": 10, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=23", "ajax": false, "filename": "EscrowService.php", "line": "23"}, "connection": "thesylink", "explain": null, "start_percent": 68.948, "width_percent": 0}, {"sql": "update \"projects\" set \"escrow_amount\" = '1154.00', \"escrow_status\" = 'held', \"escrow_created_at\" = '2025-08-30 15:06:34', \"updated_at\" = '2025-08-30 15:06:34' where \"id\" = 7", "type": "query", "params": [], "bindings": ["1154.00", "held", "2025-08-30 15:06:34", "2025-08-30 15:06:34", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 25}, {"index": 19, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.974789, "duration": 0.00691, "duration_str": "6.91ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:25", "source": {"index": 15, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=25", "ajax": false, "filename": "EscrowService.php", "line": "25"}, "connection": "thesylink", "explain": null, "start_percent": 68.948, "width_percent": 4.836}, {"sql": "insert into \"escrow_transactions\" (\"project_id\", \"client_id\", \"freelancer_id\", \"transaction_id\", \"type\", \"amount\", \"status\", \"description\", \"processed_at\", \"updated_at\", \"created_at\") values (7, 3, 6, 'escrow_hold_**********_7_68b3137af01dc', 'hold', '1154.00', 'completed', 'Escrow hold for project: AI Chatbot for Customer Service', '2025-08-30 15:06:34', '2025-08-30 15:06:34', '2025-08-30 15:06:34') returning \"id\"", "type": "query", "params": [], "bindings": [7, 3, 6, "escrow_hold_**********_7_68b3137af01dc", "hold", "1154.00", "completed", "Escrow hold for project: AI Chatbot for Customer Service", "2025-08-30 15:06:34", "2025-08-30 15:06:34", "2025-08-30 15:06:34"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 32}, {"index": 26, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 31, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.9843142, "duration": 0.013609999999999999, "duration_str": "13.61ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:32", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=32", "ajax": false, "filename": "EscrowService.php", "line": "32"}, "connection": "thesylink", "explain": null, "start_percent": 73.784, "width_percent": 9.525}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (7, 'Chapter 1', 'Complete Chapter 1 of the project', 1, 12.5, 144.25, 'pending', '2025-08-30 15:06:35', '2025-08-30 15:06:35') returning \"id\"", "type": "query", "params": [], "bindings": [7, "Chapter 1", "Complete Chapter 1 of the project", 1, 12.5, 144.25, "pending", "2025-08-30 15:06:35", "2025-08-30 15:06:35"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.000627, "duration": 0.00741, "duration_str": "7.41ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 83.309, "width_percent": 5.186}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (7, 'Chapter 2', 'Complete Chapter 2 of the project', 2, 12.5, 144.25, 'pending', '2025-08-30 15:06:35', '2025-08-30 15:06:35') returning \"id\"", "type": "query", "params": [], "bindings": [7, "Chapter 2", "Complete Chapter 2 of the project", 2, 12.5, 144.25, "pending", "2025-08-30 15:06:35", "2025-08-30 15:06:35"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.0091262, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 88.495, "width_percent": 1.785}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (7, 'Chapter 3', 'Complete Chapter 3 of the project', 3, 12.5, 144.25, 'pending', '2025-08-30 15:06:35', '2025-08-30 15:06:35') returning \"id\"", "type": "query", "params": [], "bindings": [7, "Chapter 3", "Complete Chapter 3 of the project", 3, 12.5, 144.25, "pending", "2025-08-30 15:06:35", "2025-08-30 15:06:35"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.012736, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 90.279, "width_percent": 1.547}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (7, 'Chapter 4', 'Complete Chapter 4 of the project', 4, 12.5, 144.25, 'pending', '2025-08-30 15:06:35', '2025-08-30 15:06:35') returning \"id\"", "type": "query", "params": [], "bindings": [7, "Chapter 4", "Complete Chapter 4 of the project", 4, 12.5, 144.25, "pending", "2025-08-30 15:06:35", "2025-08-30 15:06:35"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.015983, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 91.826, "width_percent": 1.449}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (7, 'Chapter 5', 'Complete Chapter 5 of the project', 5, 12.5, 144.25, 'pending', '2025-08-30 15:06:35', '2025-08-30 15:06:35') returning \"id\"", "type": "query", "params": [], "bindings": [7, "Chapter 5", "Complete Chapter 5 of the project", 5, 12.5, 144.25, "pending", "2025-08-30 15:06:35", "2025-08-30 15:06:35"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.01901, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 93.275, "width_percent": 1.701}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (7, 'Chapter 6', 'Complete Chapter 6 of the project', 6, 12.5, 144.25, 'pending', '2025-08-30 15:06:35', '2025-08-30 15:06:35') returning \"id\"", "type": "query", "params": [], "bindings": [7, "Chapter 6", "Complete Chapter 6 of the project", 6, 12.5, 144.25, "pending", "2025-08-30 15:06:35", "2025-08-30 15:06:35"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.022741, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 94.975, "width_percent": 2.17}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (7, 'Chapter 7', 'Complete Chapter 7 of the project', 7, 12.5, 144.25, 'pending', '2025-08-30 15:06:35', '2025-08-30 15:06:35') returning \"id\"", "type": "query", "params": [], "bindings": [7, "Chapter 7", "Complete Chapter 7 of the project", 7, 12.5, 144.25, "pending", "2025-08-30 15:06:35", "2025-08-30 15:06:35"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.026825, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 97.145, "width_percent": 1.442}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (7, 'Chapter 8', 'Complete Chapter 8 of the project', 8, 12.5, 144.25, 'pending', '2025-08-30 15:06:35', '2025-08-30 15:06:35') returning \"id\"", "type": "query", "params": [], "bindings": [7, "Chapter 8", "Complete Chapter 8 of the project", 8, 12.5, 144.25, "pending", "2025-08-30 15:06:35", "2025-08-30 15:06:35"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.029905, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 98.586, "width_percent": 1.414}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.034446, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EscrowService.php:23", "source": {"index": 9, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=23", "ajax": false, "filename": "EscrowService.php", "line": "23"}, "connection": "thesylink", "explain": null, "start_percent": 100, "width_percent": 0}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.051954, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BidController.php:80", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=80", "ajax": false, "filename": "BidController.php", "line": "80"}, "connection": "thesylink", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\ProjectMilestone": {"created": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProjectMilestone.php&line=1", "ajax": false, "filename": "ProjectMilestone.php", "line": "?"}}, "App\\Models\\Project": {"retrieved": 2, "updated": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Bid": {"retrieved": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FBid.php&line=1", "ajax": false, "filename": "Bid.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\EscrowTransaction": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FEscrowTransaction.php&line=1", "ajax": false, "filename": "EscrowTransaction.php", "line": "?"}}}, "count": 16, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 4, "updated": 3, "created": 9}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/projects/ai-chatbot-for-customer-service-uRET61/bids/19/accept", "action_name": "bids.accept", "controller_action": "App\\Http\\Controllers\\BidController@accept", "uri": "PATCH projects/{project}/bids/{bid}/accept", "controller": "App\\Http\\Controllers\\BidController@accept<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=57\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=57\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BidController.php:57-111</a>", "middleware": "web, auth, verified", "duration": "491ms", "peak_memory": "24MB", "response": "Redirect to http://localhost:8000/projects/ai-chatbot-for-customer-service-uRET61", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1703335610 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1703335610\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-174657873 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-174657873\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-623637794 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9GcEJEa1FXbFhoUmk3SUh6Z05KNGc9PSIsInZhbHVlIjoiL24rSVE4NVp1NmZJUEZjNHowSjc3VkZ2WVFwVis0ZFN1NG9DZXlsNXNIT3NHbys5U3pyZndvL1l1RHNkSW5CckpmTVdVSHlIRnpMSFdXODlnOGZVMEpiMWZ0b1d3L1dodWFNRGVSSXJSTlFYNjgyYjU1K0grUUJKeUl5eFVWWVkiLCJtYWMiOiI5YzNkYmQzYzE3YWNhMjliNGY1ZjFmYzVlNWMyMDcxZWE4MTQzNzQ0ZTZjYjRkMjgyOTZmYzZhYjBhOTRkMTgwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">http://localhost:8000/projects/ai-chatbot-for-customer-service-uRET61</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"958 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6Ik9GcEJEa1FXbFhoUmk3SUh6Z05KNGc9PSIsInZhbHVlIjoiL24rSVE4NVp1NmZJUEZjNHowSjc3VkZ2WVFwVis0ZFN1NG9DZXlsNXNIT3NHbys5U3pyZndvL1l1RHNkSW5CckpmTVdVSHlIRnpMSFdXODlnOGZVMEpiMWZ0b1d3L1dodWFNRGVSSXJSTlFYNjgyYjU1K0grUUJKeUl5eFVWWVkiLCJtYWMiOiI5YzNkYmQzYzE3YWNhMjliNGY1ZjFmYzVlNWMyMDcxZWE4MTQzNzQ0ZTZjYjRkMjgyOTZmYzZhYjBhOTRkMTgwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlJBUXNPRmdBaDdCeklRRjk0OUp3OFE9PSIsInZhbHVlIjoiMExSb2hvUDJtZlE1RGFmUi9VcFdhYUFZaEJSeUsvemxQSDdrajltam1YTG03R1VuYi9FYXcyeEpiR0lNL01RNVkya29OdW4xOUtFRDF3SEVUKzZMaHdwSUF5UjFNVWtNYitMQkM4dlBOTXpVOVdUUlhGdWpzOVVvbHpnVzZiV0siLCJtYWMiOiI4MDBlYjI1NGIwYWEzYjI3YjRmM2RhM2Y3YmJiYTE0OGQxZDg4YjkzNDVhZTcwYTQyMDE2NzFiOGZmOGVjZTc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623637794\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1919763690 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">01OxAJm9OpbFA90nzoy7uDuvC9OWmqYdHu2or5cI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LBsvFMJGRN2ZAIEe15GqxZoERlK01D5cKhC2UBG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1919763690\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1954079147 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 30 Aug 2025 15:06:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">http://localhost:8000/projects/ai-chatbot-for-customer-service-uRET61</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954079147\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1936537492 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">01OxAJm9OpbFA90nzoy7uDuvC9OWmqYdHu2or5cI</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Bid accepted successfully! The project has been assigned to the freelancer and escrow has been created.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936537492\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/projects/ai-chatbot-for-customer-service-uRET61/bids/19/accept", "action_name": "bids.accept", "controller_action": "App\\Http\\Controllers\\BidController@accept"}, "badge": "302 Found"}}