{"__meta": {"id": "01K3XSCPWY989MY05E0274CWX6", "datetime": "2025-08-30 15:26:31", "utime": **********.8393, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 3, "messages": [{"message": "[15:26:31] LOG.error: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function\n    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39\n    at Array.map (<anonymous>)\n    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)\n    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)\n    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)\n    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)\n    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)\n    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)\n    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)\n    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {\n    \"url\": \"http:\\/\\/localhost:8000\\/admin\\/users\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-30T15:26:31.284Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.827805, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:31] LOG.error: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function\n    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39\n    at Array.map (<anonymous>)\n    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)\n    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)\n    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)\n    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)\n    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)\n    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)\n    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)\n    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {\n    \"url\": \"http:\\/\\/localhost:8000\\/admin\\/users\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-30T15:26:31.285Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.828115, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:31] LOG.warning: %s\n\n%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.\nVisit https://react.dev/link/error-boundaries to learn more about error boundaries. {\n    \"url\": \"http:\\/\\/localhost:8000\\/admin\\/users\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-30T15:26:31.285Z\"\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.828298, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.705746, "end": **********.839313, "duration": 0.13356709480285645, "duration_str": "134ms", "measures": [{"label": "Booting", "start": **********.705746, "relative_start": 0, "end": **********.803259, "relative_end": **********.803259, "duration": 0.*****************, "duration_str": "97.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.803269, "relative_start": 0.*****************, "end": **********.839321, "relative_end": 7.867813110351562e-06, "duration": 0.*****************, "duration_str": "36.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.816246, "relative_start": 0.*****************, "end": **********.817728, "relative_end": **********.817728, "duration": 0.0014820098876953125, "duration_str": "1.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.837815, "relative_start": 0.*****************, "end": **********.838051, "relative_end": **********.838051, "duration": 0.00023603439331054688, "duration_str": "236μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.838067, "relative_start": 0.*****************, "end": **********.838083, "relative_end": **********.838083, "duration": 1.5974044799804688e-05, "duration_str": "16μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1153}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 971}], "start": **********.835843, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:100-126</a>", "duration": "134ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-484307446 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">uncaught_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-30T15:26:31.284Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Uncaught TypeError: user.wallet_balance.toFixed is not a function</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>687</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>39</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">TypeError</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"45 characters\">user.wallet_balance.toFixed is not a function</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1021 characters\">TypeError: user.wallet_balance.toFixed is not a function<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at Array.map (&lt;anonymous&gt;)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/users</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">window_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-30T15:26:31.285Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Uncaught TypeError: user.wallet_balance.toFixed is not a function</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>687</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>39</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">TypeError</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"45 characters\">user.wallet_balance.toFixed is not a function</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1021 characters\">TypeError: user.wallet_balance.toFixed is not a function<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at Array.map (&lt;anonymous&gt;)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1021 characters\">    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/users</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">warn</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-30T15:26:31.285Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"\"\"\n          <span class=sf-dump-str title=\"6 characters\">%s<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"6 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"6 characters\">%s</span>\n          \"\"\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"52 characters\">An error occurred in the &lt;UserManagement&gt; component.</span>\"\n        <span class=sf-dump-index>2</span> => \"\"\"\n          <span class=sf-dump-str title=\"168 characters\">Consider adding an error boundary to your tree to customize error handling behavior.<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"168 characters\">Visit https://react.dev/link/error-boundaries to learn more about error boundaries.</span>\n          \"\"\"\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/users</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484307446\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-201025337 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3580</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"958 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6InE4aE1NR1pQQlEzNEJGQzJFaGFETkE9PSIsInZhbHVlIjoiazZ0cWNpZ2xHblZCaWhlcExrcXJrWWZrbFp1dVI1Z0Vmc3YyRXZGNWx1SkZ1b2ZtZ0tobHc4KzZVWVJST2tXMTFaeTQrWmhTT1VLZEJjRVdFNmU4eFAwYjU0RG90M2NQRllRY09vcXRVUHRLeStqV3E5dU1pbVNuSmhFQ3ZJeDkiLCJtYWMiOiI2OGQ0MDIxZmM0MDc3ZWU2ODkxY2Q3NDM5ZjBjMjBmYmYyMjMxNzc4M2U4Mjc1MTVlYTgzMzQ0OWNjNGEwOGNjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InRjQnYwQmxFME5rZ0l6empjdWI5NWc9PSIsInZhbHVlIjoiNlVMNDZWZ2tPSS94WXc0TUI0SDE0dlFFZ0ZtTW9OK1Z3QUFueHJOMFdsdWYrTTA1U1ZCSUt2YWk2WmxOZkVtWEVmNXRXNlRrZGhOalVWYnA4bVo1M1pvL2x3YytuK2RlUzJLa0xSSFJHeHNaQzA3QUVBSXNPMDZCdU9xTkxhSzciLCJtYWMiOiI0OWI2OTVkZTRkM2RjMzQ3NGFhYWQ2YTRlMzZmYzVkNzAwYTYwM2Q4NGQzNGM5NGE1ZDZiZjM5YmZjZGI1MzEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201025337\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-768960577 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ba5658bd4fdaa31823eff4f3ddd8fd98</span>\"\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => \"<span class=sf-dump-str title=\"32 characters\">6ed91cf578d851379b68c861c0577493</span>\"\n  \"<span class=sf-dump-key>pma_lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => \"<span class=sf-dump-str title=\"81 characters\">cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InE4aE1NR1pQQlEzNEJGQzJFaGFETkE9PSIsInZhbHVlIjoiazZ0cWNpZ2xHblZCaWhlcExrcXJrWWZrbFp1dVI1Z0Vmc3YyRXZGNWx1SkZ1b2ZtZ0tobHc4KzZVWVJST2tXMTFaeTQrWmhTT1VLZEJjRVdFNmU4eFAwYjU0RG90M2NQRllRY09vcXRVUHRLeStqV3E5dU1pbVNuSmhFQ3ZJeDkiLCJtYWMiOiI2OGQ0MDIxZmM0MDc3ZWU2ODkxY2Q3NDM5ZjBjMjBmYmYyMjMxNzc4M2U4Mjc1MTVlYTgzMzQ0OWNjNGEwOGNjIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InRjQnYwQmxFME5rZ0l6empjdWI5NWc9PSIsInZhbHVlIjoiNlVMNDZWZ2tPSS94WXc0TUI0SDE0dlFFZ0ZtTW9OK1Z3QUFueHJOMFdsdWYrTTA1U1ZCSUt2YWk2WmxOZkVtWEVmNXRXNlRrZGhOalVWYnA4bVo1M1pvL2x3YytuK2RlUzJLa0xSSFJHeHNaQzA3QUVBSXNPMDZCdU9xTkxhSzciLCJtYWMiOiI0OWI2OTVkZTRkM2RjMzQ3NGFhYWQ2YTRlMzZmYzVkNzAwYTYwM2Q4NGQzNGM5NGE1ZDZiZjM5YmZjZGI1MzEwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-768960577\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1562742180 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 30 Aug 2025 15:26:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562742180\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2095690313 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2095690313\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}