{"__meta": {"id": "01K3XNFV19M16YC7E2GN51P8TR", "datetime": "2025-08-30 14:18:20", "utime": **********.074076, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 3, "messages": [{"message": "[14:18:20] LOG.error: Uncaught ReferenceError: Link is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500 811 15 ReferenceError Link is not defined ReferenceError: Link is not defined\n    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500:811:15)\n    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)\n    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)\n    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)\n    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)\n    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)\n    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)\n    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)\n    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)\n    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {\n    \"url\": \"http:\\/\\/localhost:8000\\/projects\\/e-commerce-website-development-YEOOQW\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-30T14:18:19.744Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.053173, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:20] LOG.error: Uncaught ReferenceError: Link is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500 811 15 ReferenceError Link is not defined ReferenceError: Link is not defined\n    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500:811:15)\n    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)\n    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)\n    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)\n    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)\n    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)\n    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)\n    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)\n    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)\n    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {\n    \"url\": \"http:\\/\\/localhost:8000\\/projects\\/e-commerce-website-development-YEOOQW\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-30T14:18:19.744Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.053555, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:20] LOG.warning: %s\n\n%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.\nVisit https://react.dev/link/error-boundaries to learn more about error boundaries. {\n    \"url\": \"http:\\/\\/localhost:8000\\/projects\\/e-commerce-website-development-YEOOQW\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-30T14:18:19.747Z\"\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.054711, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": 1756563499.888245, "end": **********.074107, "duration": 0.18586182594299316, "duration_str": "186ms", "measures": [{"label": "Booting", "start": 1756563499.888245, "relative_start": 0, "end": **********.027267, "relative_end": **********.027267, "duration": 0.****************, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.027276, "relative_start": 0.*****************, "end": **********.074111, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "46.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.042604, "relative_start": 0.****************, "end": **********.044718, "relative_end": **********.044718, "duration": 0.0021140575408935547, "duration_str": "2.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.071266, "relative_start": 0.*****************, "end": **********.071761, "relative_end": **********.071761, "duration": 0.0004949569702148438, "duration_str": "495μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.071789, "relative_start": 0.*****************, "end": **********.071815, "relative_end": **********.071815, "duration": 2.5987625122070312e-05, "duration_str": "26μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1153}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 971}], "start": **********.066273, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:100-126</a>", "duration": "187ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-667809631 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-667809631\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-156025785 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">uncaught_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-30T14:18:19.744Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Uncaught ReferenceError: Link is not defined</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"70 characters\">http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>811</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>15</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">ReferenceError</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Link is not defined</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1113 characters\">ReferenceError: Link is not defined<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500:811:15)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"68 characters\">http://localhost:8000/projects/e-commerce-website-development-YEOOQW</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">window_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-30T14:18:19.744Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Uncaught ReferenceError: Link is not defined</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"70 characters\">http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>811</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>15</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">ReferenceError</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Link is not defined</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1113 characters\">ReferenceError: Link is not defined<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500:811:15)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1113 characters\">    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"68 characters\">http://localhost:8000/projects/e-commerce-website-development-YEOOQW</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">warn</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-30T14:18:19.747Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"\"\"\n          <span class=sf-dump-str title=\"6 characters\">%s<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"6 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"6 characters\">%s</span>\n          \"\"\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"49 characters\">An error occurred in the &lt;ShowProject&gt; component.</span>\"\n        <span class=sf-dump-index>2</span> => \"\"\"\n          <span class=sf-dump-str title=\"168 characters\">Consider adding an error boundary to your tree to customize error handling behavior.<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"168 characters\">Visit https://react.dev/link/error-boundaries to learn more about error boundaries.</span>\n          \"\"\"\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"68 characters\">http://localhost:8000/projects/e-commerce-website-development-YEOOQW</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156025785\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-298439932 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3800</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">http://localhost:8000/projects/e-commerce-website-development-YEOOQW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"958 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6ImhURm9tTnlJYXpxd2s4bGQ5S2duZnc9PSIsInZhbHVlIjoiNW42cHlGK0NRUVhUOGE2K0hqS3RPazBORDdnVHBWWjFlUHhpc2tKaWVOcVJocS90N2kvZ3IrRUhOS3BWODdSSmd4d1BydVJuYlZpU3VJaHdxVVdrbm5PUnJWK1RrNW1NcWU0ZE01RmhuVWJWazhhdWRsWUlXMzhTWEJNYzRJTk4iLCJtYWMiOiJjODA2MzIzZWZkYjUyM2M5MTI3MmUxZTA4ZjdkNzdiN2EwZDA3ZDBlNjMwNjhhMjJhN2I4YjcwZGJkMmFkYTkyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkdUdk0zMm92NTR1WUthSWNIMVNpaHc9PSIsInZhbHVlIjoiZ2dLRmJPbWtBTkk3Y0VwN3RQK1ZHZ0ZjV3hGLzRXS2dPWjRrTE96L1BLMFg1OUpwd3dlQ243MkJJVTUrL1Fxd0QrdnIvdXViMVdzZUdmMU9YRTd5ckRoNGtiNHQrazVjUERoczh0T2s2NEtmUGVXTEJrQm5uMXlkVWNzK0Y5Mk0iLCJtYWMiOiI0Yjk1OTFjODJkZmI4NmMxZWJkMmRjM2ZiZDVmZDQwNjMzNTNmNGNhNmE4Mjc5NDQ4MTA1MjVlMzVjZWM1N2NkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298439932\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1058647771 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ba5658bd4fdaa31823eff4f3ddd8fd98</span>\"\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => \"<span class=sf-dump-str title=\"32 characters\">6ed91cf578d851379b68c861c0577493</span>\"\n  \"<span class=sf-dump-key>pma_lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => \"<span class=sf-dump-str title=\"81 characters\">cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImhURm9tTnlJYXpxd2s4bGQ5S2duZnc9PSIsInZhbHVlIjoiNW42cHlGK0NRUVhUOGE2K0hqS3RPazBORDdnVHBWWjFlUHhpc2tKaWVOcVJocS90N2kvZ3IrRUhOS3BWODdSSmd4d1BydVJuYlZpU3VJaHdxVVdrbm5PUnJWK1RrNW1NcWU0ZE01RmhuVWJWazhhdWRsWUlXMzhTWEJNYzRJTk4iLCJtYWMiOiJjODA2MzIzZWZkYjUyM2M5MTI3MmUxZTA4ZjdkNzdiN2EwZDA3ZDBlNjMwNjhhMjJhN2I4YjcwZGJkMmFkYTkyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdUdk0zMm92NTR1WUthSWNIMVNpaHc9PSIsInZhbHVlIjoiZ2dLRmJPbWtBTkk3Y0VwN3RQK1ZHZ0ZjV3hGLzRXS2dPWjRrTE96L1BLMFg1OUpwd3dlQ243MkJJVTUrL1Fxd0QrdnIvdXViMVdzZUdmMU9YRTd5ckRoNGtiNHQrazVjUERoczh0T2s2NEtmUGVXTEJrQm5uMXlkVWNzK0Y5Mk0iLCJtYWMiOiI0Yjk1OTFjODJkZmI4NmMxZWJkMmRjM2ZiZDVmZDQwNjMzNTNmNGNhNmE4Mjc5NDQ4MTA1MjVlMzVjZWM1N2NkIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058647771\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-976389597 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 30 Aug 2025 14:18:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976389597\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1486271412 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1486271412\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}