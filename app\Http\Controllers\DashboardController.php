<?php

namespace App\Http\Controllers;

use App\Models\Project;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        $userId = Auth::id();
        $projects = Project::with(['files'])
            ->where('user_id', $userId)
            ->latest()
            ->get();

        return Inertia::render('dashboard', [
            'projects' => $projects->map(function ($project) {
                return [
                    'id' => $project->id,
                    'title' => $project->title,
                    'slug' => $project->slug,
                    'description' => $project->description,
                    'budget_min' => $project->budget_min ? (float) $project->budget_min : null,
                    'budget_max' => $project->budget_max ? (float) $project->budget_max : null,
                    'budget_type' => $project->budget_type,
                    'deadline' => $project->deadline?->format('Y-m-d'),
                    'category' => $project->category,
                    'academic_level' => $project->academic_level,
                    'status' => $project->status,
                    'file_count' => $project->file_count,
                    'created_at' => $project->created_at->toISOString(),
                ];
            }),
        ]);
    }
}
