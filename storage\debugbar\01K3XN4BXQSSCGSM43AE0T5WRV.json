{"__meta": {"id": "01K3XN4BXQSSCGSM43AE0T5WRV", "datetime": "2025-08-30 14:12:04", "utime": **********.152357, "method": "PATCH", "uri": "/projects/e-commerce-website-development-YEOOQW/bids/2/accept", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.772076, "end": **********.152377, "duration": 0.38030099868774414, "duration_str": "380ms", "measures": [{"label": "Booting", "start": **********.772076, "relative_start": 0, "end": **********.906567, "relative_end": **********.906567, "duration": 0.****************, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.906579, "relative_start": 0.*****************, "end": **********.152379, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "246ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.922651, "relative_start": 0.****************, "end": **********.924863, "relative_end": **********.924863, "duration": 0.002212047576904297, "duration_str": "2.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.149068, "relative_start": 0.*****************, "end": **********.149362, "relative_end": **********.149362, "duration": 0.00029397010803222656, "duration_str": "294μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 23, "nb_statements": 18, "nb_visible_statements": 23, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.13529000000000002, "accumulated_duration_str": "135ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.93389, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = '10NUWduTBRHmdNtutFgkAQxuCDDi6vnYMwP7lkcu' limit 1", "type": "query", "params": [], "bindings": ["10NUWduTBRHmdNtutFgkAQxuCDDi6vnYMwP7lkcu"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.939926, "duration": 0.05353, "duration_str": "53.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 39.567}, {"sql": "select * from \"users\" where \"id\" = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.0035691, "duration": 0.00609, "duration_str": "6.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 39.567, "width_percent": 4.501}, {"sql": "select * from \"projects\" where \"slug\" = 'e-commerce-website-development-YEOOQW' limit 1", "type": "query", "params": [], "bindings": ["e-commerce-website-development-YEOOQW"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 980}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.0135012, "duration": 0.00484, "duration_str": "4.84ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "thesylink", "explain": null, "start_percent": 44.068, "width_percent": 3.578}, {"sql": "select * from \"bids\" where \"id\" = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 980}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.0203269, "duration": 0.017070000000000002, "duration_str": "17.07ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "thesylink", "explain": null, "start_percent": 47.646, "width_percent": 12.617}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.046313, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BidController.php:80", "source": {"index": 10, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=80", "ajax": false, "filename": "BidController.php", "line": "80"}, "connection": "thesylink", "explain": null, "start_percent": 60.263, "width_percent": 0}, {"sql": "update \"bids\" set \"status\" = 'accepted', \"updated_at\" = '2025-08-30 14:12:04' where \"id\" = 2", "type": "query", "params": [], "bindings": ["accepted", "2025-08-30 14:12:04", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 82}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.046858, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "BidController.php:82", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=82", "ajax": false, "filename": "BidController.php", "line": "82"}, "connection": "thesylink", "explain": null, "start_percent": 60.263, "width_percent": 2.868}, {"sql": "update \"bids\" set \"status\" = 'rejected', \"updated_at\" = '2025-08-30 14:12:04' where \"project_id\" = 1 and \"id\" != 2 and \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["rejected", "2025-08-30 14:12:04", 1, 2, "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.054106, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "BidController.php:88", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=88", "ajax": false, "filename": "BidController.php", "line": "88"}, "connection": "thesylink", "explain": null, "start_percent": 63.131, "width_percent": 2.166}, {"sql": "update \"projects\" set \"status\" = 'in_progress', \"assigned_freelancer_id\" = 6, \"accepted_bid_amount\" = '1188.00', \"assigned_at\" = '2025-08-30 14:12:04', \"updated_at\" = '2025-08-30 14:12:04' where \"id\" = 1", "type": "query", "params": [], "bindings": ["in_progress", 6, "1188.00", "2025-08-30 14:12:04", "2025-08-30 14:12:04", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.059859, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "BidController.php:91", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=91", "ajax": false, "filename": "BidController.php", "line": "91"}, "connection": "thesylink", "explain": null, "start_percent": 65.297, "width_percent": 3.141}, {"sql": "select * from \"projects\" where \"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.065886, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "BidController.php:100", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=100", "ajax": false, "filename": "BidController.php", "line": "100"}, "connection": "thesylink", "explain": null, "start_percent": 68.438, "width_percent": 2.328}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.075951, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EscrowService.php:23", "source": {"index": 10, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=23", "ajax": false, "filename": "EscrowService.php", "line": "23"}, "connection": "thesylink", "explain": null, "start_percent": 70.767, "width_percent": 0}, {"sql": "update \"projects\" set \"escrow_amount\" = '1188.00', \"escrow_status\" = 'held', \"escrow_created_at\" = '2025-08-30 14:12:04', \"updated_at\" = '2025-08-30 14:12:04' where \"id\" = 1", "type": "query", "params": [], "bindings": ["1188.00", "held", "2025-08-30 14:12:04", "2025-08-30 14:12:04", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 25}, {"index": 19, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.076827, "duration": 0.006, "duration_str": "6ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:25", "source": {"index": 15, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=25", "ajax": false, "filename": "EscrowService.php", "line": "25"}, "connection": "thesylink", "explain": null, "start_percent": 70.767, "width_percent": 4.435}, {"sql": "insert into \"escrow_transactions\" (\"project_id\", \"client_id\", \"freelancer_id\", \"transaction_id\", \"type\", \"amount\", \"status\", \"description\", \"processed_at\", \"updated_at\", \"created_at\") values (1, 3, 6, 'escrow_hold_**********_1_68b306b414bcb', 'hold', '1188.00', 'completed', 'Escrow hold for project: E-Commerce Website Development', '2025-08-30 14:12:04', '2025-08-30 14:12:04', '2025-08-30 14:12:04') returning \"id\"", "type": "query", "params": [], "bindings": [1, 3, 6, "escrow_hold_**********_1_68b306b414bcb", "hold", "1188.00", "completed", "Escrow hold for project: E-Commerce Website Development", "2025-08-30 14:12:04", "2025-08-30 14:12:04", "2025-08-30 14:12:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 32}, {"index": 26, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 31, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.085889, "duration": 0.00962, "duration_str": "9.62ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:32", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=32", "ajax": false, "filename": "EscrowService.php", "line": "32"}, "connection": "thesylink", "explain": null, "start_percent": 75.201, "width_percent": 7.111}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (1, 'Chapter 1', 'Complete Chapter 1 of the project', 1, 12.5, 148.5, 'pending', '2025-08-30 14:12:04', '2025-08-30 14:12:04') returning \"id\"", "type": "query", "params": [], "bindings": [1, "Chapter 1", "Complete Chapter 1 of the project", 1, 12.5, 148.5, "pending", "2025-08-30 14:12:04", "2025-08-30 14:12:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.097222, "duration": 0.00819, "duration_str": "8.19ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 82.312, "width_percent": 6.054}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (1, 'Chapter 2', 'Complete Chapter 2 of the project', 2, 12.5, 148.5, 'pending', '2025-08-30 14:12:04', '2025-08-30 14:12:04') returning \"id\"", "type": "query", "params": [], "bindings": [1, "Chapter 2", "Complete Chapter 2 of the project", 2, 12.5, 148.5, "pending", "2025-08-30 14:12:04", "2025-08-30 14:12:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.106366, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 88.366, "width_percent": 1.589}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (1, 'Chapter 3', 'Complete Chapter 3 of the project', 3, 12.5, 148.5, 'pending', '2025-08-30 14:12:04', '2025-08-30 14:12:04') returning \"id\"", "type": "query", "params": [], "bindings": [1, "Chapter 3", "Complete Chapter 3 of the project", 3, 12.5, 148.5, "pending", "2025-08-30 14:12:04", "2025-08-30 14:12:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.1093671, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 89.955, "width_percent": 1.382}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (1, 'Chapter 4', 'Complete Chapter 4 of the project', 4, 12.5, 148.5, 'pending', '2025-08-30 14:12:04', '2025-08-30 14:12:04') returning \"id\"", "type": "query", "params": [], "bindings": [1, "Chapter 4", "Complete Chapter 4 of the project", 4, 12.5, 148.5, "pending", "2025-08-30 14:12:04", "2025-08-30 14:12:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.112039, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 91.337, "width_percent": 1.419}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (1, 'Chapter 5', 'Complete Chapter 5 of the project', 5, 12.5, 148.5, 'pending', '2025-08-30 14:12:04', '2025-08-30 14:12:04') returning \"id\"", "type": "query", "params": [], "bindings": [1, "Chapter 5", "Complete Chapter 5 of the project", 5, 12.5, 148.5, "pending", "2025-08-30 14:12:04", "2025-08-30 14:12:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.114769, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 92.756, "width_percent": 1.419}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (1, 'Chapter 6', 'Complete Chapter 6 of the project', 6, 12.5, 148.5, 'pending', '2025-08-30 14:12:04', '2025-08-30 14:12:04') returning \"id\"", "type": "query", "params": [], "bindings": [1, "Chapter 6", "Complete Chapter 6 of the project", 6, 12.5, 148.5, "pending", "2025-08-30 14:12:04", "2025-08-30 14:12:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.117847, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 94.175, "width_percent": 2.498}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (1, 'Chapter 7', 'Complete Chapter 7 of the project', 7, 12.5, 148.5, 'pending', '2025-08-30 14:12:04', '2025-08-30 14:12:04') returning \"id\"", "type": "query", "params": [], "bindings": [1, "Chapter 7", "Complete Chapter 7 of the project", 7, 12.5, 148.5, "pending", "2025-08-30 14:12:04", "2025-08-30 14:12:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.122299, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 96.674, "width_percent": 1.656}, {"sql": "insert into \"project_milestones\" (\"project_id\", \"title\", \"description\", \"order_index\", \"payment_percentage\", \"payment_amount\", \"status\", \"updated_at\", \"created_at\") values (1, 'Chapter 8', 'Complete Chapter 8 of the project', 8, 12.5, 148.5, 'pending', '2025-08-30 14:12:04', '2025-08-30 14:12:04') returning \"id\"", "type": "query", "params": [], "bindings": [1, "Chapter 8", "Complete Chapter 8 of the project", 8, 12.5, 148.5, "pending", "2025-08-30 14:12:04", "2025-08-30 14:12:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, {"index": 23, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}], "start": **********.125467, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "EscrowService.php:68", "source": {"index": 22, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=68", "ajax": false, "filename": "EscrowService.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 98.33, "width_percent": 1.67}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 100}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.129658, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EscrowService.php:23", "source": {"index": 9, "namespace": null, "name": "app/Services/EscrowService.php", "file": "C:\\dev\\thesylink\\app\\Services\\EscrowService.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FServices%2FEscrowService.php&line=23", "ajax": false, "filename": "EscrowService.php", "line": "23"}, "connection": "thesylink", "explain": null, "start_percent": 100, "width_percent": 0}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.145773, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BidController.php:80", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/BidController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\BidController.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=80", "ajax": false, "filename": "BidController.php", "line": "80"}, "connection": "thesylink", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\ProjectMilestone": {"created": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProjectMilestone.php&line=1", "ajax": false, "filename": "ProjectMilestone.php", "line": "?"}}, "App\\Models\\Project": {"retrieved": 2, "updated": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Bid": {"retrieved": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FBid.php&line=1", "ajax": false, "filename": "Bid.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\EscrowTransaction": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FEscrowTransaction.php&line=1", "ajax": false, "filename": "EscrowTransaction.php", "line": "?"}}}, "count": 16, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 4, "updated": 3, "created": 9}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/projects/e-commerce-website-development-YEOOQW/bids/2/accept", "action_name": "bids.accept", "controller_action": "App\\Http\\Controllers\\BidController@accept", "uri": "PATCH projects/{project}/bids/{bid}/accept", "controller": "App\\Http\\Controllers\\BidController@accept<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=57\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FBidController.php&line=57\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BidController.php:57-111</a>", "middleware": "web, auth, verified", "duration": "383ms", "peak_memory": "24MB", "response": "Redirect to http://localhost:8000/projects/e-commerce-website-development-YEOOQW", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2029162261 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2029162261\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1875958269 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1875958269\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1076947757 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZxY0FGdG1MK3pQREl2NGdjNUhqWVE9PSIsInZhbHVlIjoiM3RQUVRrODhZLzFUSmM4dmY5NUJyTWRGNU56aGppZmxhZ3NMRkdCV1FtVGxuMVp5THdUME5SY1VnMFhVQWtCSFc4VlhFVExPalhiVE4wZ2dCOG96U0lQR3VSNXlMNUkxcHM3OHdwMm80TURTUUtZeUZEYnY0Qm90dUYrekl0TzEiLCJtYWMiOiIyYTE3MmQ3ZDBlY2Y2ZTI4MjBjOGE3ZmNlMjk3YjQ2YmQ0YjI4ZjI5N2YyMjQzNmRkOWI0YTllNzI2MGQxYjkwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">http://localhost:8000/projects/e-commerce-website-development-YEOOQW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"958 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6IkZxY0FGdG1MK3pQREl2NGdjNUhqWVE9PSIsInZhbHVlIjoiM3RQUVRrODhZLzFUSmM4dmY5NUJyTWRGNU56aGppZmxhZ3NMRkdCV1FtVGxuMVp5THdUME5SY1VnMFhVQWtCSFc4VlhFVExPalhiVE4wZ2dCOG96U0lQR3VSNXlMNUkxcHM3OHdwMm80TURTUUtZeUZEYnY0Qm90dUYrekl0TzEiLCJtYWMiOiIyYTE3MmQ3ZDBlY2Y2ZTI4MjBjOGE3ZmNlMjk3YjQ2YmQ0YjI4ZjI5N2YyMjQzNmRkOWI0YTllNzI2MGQxYjkwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InhzZU0rR2NwQnZHVSszQ0JOdG53MVE9PSIsInZhbHVlIjoiNTBiWHJyQ3JWUWpCNXdDTkM3UkY3S2dVOE4vdUVqRmpSa1hxNkNOclVWQ2dneGZqL1hlTURnMWJXSnJBM2I2SGwyRmxKYjFPdVNMQ0xHUnlubCtuc0hGdW5FaUltL08xanRDb1lBQ052ODN0eElnNkZ0QzFPdnJISngrQndFRTAiLCJtYWMiOiI4YjliMmYzYzZkZGMwYWJjNzA1MjY5ZmJjZDlhMDYyMTQyNjk3MzgyZjI1MTYxZjAzZTQ2ODIwMjYwYTlhZmFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076947757\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nZwev9ZMUH22PhhuDxw4ohrZZr7h78ITdYJZhVdA</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">10NUWduTBRHmdNtutFgkAQxuCDDi6vnYMwP7lkcu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-380563455 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 30 Aug 2025 14:12:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">http://localhost:8000/projects/e-commerce-website-development-YEOOQW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380563455\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1594322556 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nZwev9ZMUH22PhhuDxw4ohrZZr7h78ITdYJZhVdA</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K3XN3E54FTGRKHJFP8F5AC2T</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Bid accepted successfully! The project has been assigned to the freelancer and escrow has been created.</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594322556\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/projects/e-commerce-website-development-YEOOQW/bids/2/accept", "action_name": "bids.accept", "controller_action": "App\\Http\\Controllers\\BidController@accept"}, "badge": "302 Found"}}