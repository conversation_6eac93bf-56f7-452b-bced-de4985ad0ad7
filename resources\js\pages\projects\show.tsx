import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Calendar, DollarSign, FileText, MessageSquare, User } from 'lucide-react';
import { useState } from 'react';

interface Bid {
    id: number;
    amount: number;
    proposal: string;
    delivery_days: number;
    status: 'pending' | 'accepted' | 'rejected';
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
    };
}

interface Project {
    id: number;
    title: string;
    slug: string;
    description: string;
    requirements?: string;
    budget_min?: number;
    budget_max?: number;
    budget_type: 'fixed' | 'hourly' | 'negotiable';
    deadline?: string;
    category?: string;
    academic_level?: string;
    status: 'open' | 'in_progress' | 'completed' | 'cancelled';
    file_count: number;
    created_at: string;
    assigned_freelancer_id?: number;
    accepted_bid_amount?: number;
    assigned_at?: string;
    user: {
        id: number;
        name: string;
    };
    files: Array<{
        id: number;
        original_name: string;
        file_size: number;
        created_at: string;
    }>;
    bids?: Bid[];
    assigned_freelancer?: {
        id: number;
        name: string;
        email: string;
    };
}

interface ProjectShowProps {
    project: Project;
}

const statusColors = {
    open: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    in_progress: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    completed: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
    cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

const statusLabels = {
    open: 'Open',
    in_progress: 'In Progress',
    completed: 'Completed',
    cancelled: 'Cancelled',
};

export default function ShowProject({ project }: ProjectShowProps) {
    const [bidData, setBidData] = useState({
        amount: '',
        proposal: '',
        delivery_days: '',
    });
    const [showBidForm, setShowBidForm] = useState(false);
    const [processing, setProcessing] = useState(false);

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Browse Projects',
            href: '/browse',
        },
        {
            title: project.title,
            href: `/projects/${project.id}`,
        },
    ];

    const formatBudget = () => {
        if (project.budget_type === 'negotiable') {
            return 'Negotiable';
        }

        const budgetMin = project.budget_min ? Number(project.budget_min) : null;
        const budgetMax = project.budget_max ? Number(project.budget_max) : null;

        if (budgetMin && budgetMax) {
            return `₵${budgetMin.toFixed(2)} - ₵${budgetMax.toFixed(2)}`;
        }

        if (budgetMin) {
            return `₵${budgetMin.toFixed(2)}${project.budget_type === 'hourly' ? '/hr' : ''}`;
        }

        return 'Not specified';
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatFileSize = (bytes: number) => {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;

        while (size > 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(2)} ${units[unitIndex]}`;
    };

    const handleBidSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setProcessing(true);

        router.post(`/projects/${project.slug}/bids`, bidData, {
            onSuccess: () => {
                setShowBidForm(false);
                setBidData({ amount: '', proposal: '', delivery_days: '' });
                setProcessing(false);
            },
            onError: () => {
                setProcessing(false);
            },
        });
    };

    const handleAcceptBid = (bidId: number) => {
        router.patch(
            `/projects/${project.slug}/bids/${bidId}/accept`,
            {},
            {
                onSuccess: () => {
                    // Refresh the page to show updated data
                },
                onError: (errors) => {
                    console.error('Failed to accept bid:', errors);
                },
            },
        );
    };

    const handleRejectBid = (bidId: number) => {
        router.patch(
            `/projects/${project.slug}/bids/${bidId}/reject`,
            {},
            {
                onSuccess: () => {
                    // Refresh the page to show updated data
                },
                onError: (errors) => {
                    console.error('Failed to reject bid:', errors);
                },
            },
        );
    };

    const { auth } = usePage<{ auth: { user: { id: number } } }>().props;
    const isProjectOwner = auth.user.id === project.user.id;

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={project.title} />

            <div className="mx-auto max-w-4xl space-y-6">
                {/* Project Header */}
                <Card>
                    <CardHeader>
                        <div className="flex items-start justify-between">
                            <div className="flex-1">
                                <CardTitle className="mb-2 text-2xl">{project.title}</CardTitle>
                                <div className="mb-4 flex flex-wrap gap-2">
                                    <Badge variant="secondary">{project.category}</Badge>
                                    <Badge variant="outline">{project.academic_level}</Badge>
                                    <Badge className={statusColors[project.status]}>{statusLabels[project.status]}</Badge>
                                </div>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 gap-4 border-t pt-4 md:grid-cols-3">
                            <div className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <p className="text-sm text-muted-foreground">Budget</p>
                                    <p className="font-semibold">{formatBudget()}</p>
                                </div>
                            </div>

                            {project.deadline && (
                                <div className="flex items-center gap-2">
                                    <Calendar className="h-5 w-5 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm text-muted-foreground">Deadline</p>
                                        <p className="font-semibold">{formatDate(project.deadline)}</p>
                                    </div>
                                </div>
                            )}

                            <div className="flex items-center gap-2">
                                <User className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <p className="text-sm text-muted-foreground">Posted by</p>
                                    <p className="font-semibold">{project.user.name}</p>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                </Card>

                {/* Project Description */}
                <Card>
                    <CardHeader>
                        <CardTitle>Project Description</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="prose prose-sm max-w-none">
                            <p className="whitespace-pre-wrap">{project.description}</p>
                        </div>
                    </CardContent>
                </Card>

                {/* Requirements */}
                {project.requirements && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Requirements</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="prose prose-sm max-w-none">
                                <p className="whitespace-pre-wrap">{project.requirements}</p>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Project Files */}
                {project.files.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Project Files</CardTitle>
                            <CardDescription>
                                {project.files.length} file{project.files.length !== 1 ? 's' : ''} attached
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                {project.files.map((file) => (
                                    <div key={file.id} className="flex items-center justify-between rounded-lg bg-muted p-3">
                                        <div className="flex items-center space-x-3">
                                            <FileText className="h-5 w-5 text-muted-foreground" />
                                            <div>
                                                <p className="text-sm font-medium">{file.original_name}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {formatFileSize(file.file_size)} • {formatDate(file.created_at)}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Bidding Section - Only show for non-owners when project is open */}
                {project.status === 'open' && !isProjectOwner && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Submit Your Proposal</CardTitle>
                            <CardDescription>Interested in this project? Submit your proposal and get started!</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {!showBidForm ? (
                                <Button onClick={() => setShowBidForm(true)} className="w-full">
                                    <MessageSquare className="mr-2 h-4 w-4" />
                                    Submit Proposal
                                </Button>
                            ) : (
                                <form onSubmit={handleBidSubmit} className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="amount">Your Bid Amount (₵)</Label>
                                            <Input
                                                id="amount"
                                                type="number"
                                                min="1"
                                                step="0.01"
                                                value={bidData.amount}
                                                onChange={(e) => setBidData((prev) => ({ ...prev, amount: e.target.value }))}
                                                placeholder="Enter your bid amount"
                                                required
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="delivery_days">Delivery Time (Days)</Label>
                                            <Input
                                                id="delivery_days"
                                                type="number"
                                                min="1"
                                                max="365"
                                                value={bidData.delivery_days}
                                                onChange={(e) => setBidData((prev) => ({ ...prev, delivery_days: e.target.value }))}
                                                placeholder="Number of days"
                                                required
                                            />
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="proposal">Your Proposal</Label>
                                        <Textarea
                                            id="proposal"
                                            value={bidData.proposal}
                                            onChange={(e) => setBidData((prev) => ({ ...prev, proposal: e.target.value }))}
                                            placeholder="Describe your approach, experience, and why you're the best fit for this project..."
                                            rows={6}
                                            required
                                        />
                                        <p className="text-sm text-muted-foreground">Minimum 50 characters</p>
                                    </div>

                                    <div className="flex gap-2">
                                        <Button type="submit" disabled={processing}>
                                            {processing ? 'Submitting...' : 'Submit Proposal'}
                                        </Button>
                                        <Button type="button" variant="outline" onClick={() => setShowBidForm(false)}>
                                            Cancel
                                        </Button>
                                    </div>
                                </form>
                            )}
                        </CardContent>
                    </Card>
                )}

                {/* Bid Management Section - Only visible to project owner */}
                {isProjectOwner && project.bids && project.bids.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Received Proposals ({project.bids.length})</CardTitle>
                            <CardDescription>Review and manage proposals from freelancers</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {project.bids.map((bid) => (
                                    <div key={bid.id} className="rounded-lg border p-4">
                                        <div className="flex items-start justify-between">
                                            <div className="flex-1">
                                                <div className="mb-2 flex items-center gap-2">
                                                    <User className="h-4 w-4 text-muted-foreground" />
                                                    <span className="font-medium">{bid.user.name}</span>
                                                    <Badge
                                                        variant={
                                                            bid.status === 'accepted'
                                                                ? 'default'
                                                                : bid.status === 'rejected'
                                                                  ? 'destructive'
                                                                  : 'secondary'
                                                        }
                                                    >
                                                        {bid.status}
                                                    </Badge>
                                                </div>
                                                <div className="mb-3 grid grid-cols-1 gap-4 md:grid-cols-3">
                                                    <div>
                                                        <p className="text-sm text-muted-foreground">Bid Amount</p>
                                                        <p className="font-semibold">₵{Number(bid.amount).toFixed(2)}</p>
                                                    </div>
                                                    <div>
                                                        <p className="text-sm text-muted-foreground">Delivery Time</p>
                                                        <p className="font-semibold">{bid.delivery_days} days</p>
                                                    </div>
                                                    <div>
                                                        <p className="text-sm text-muted-foreground">Submitted</p>
                                                        <p className="font-semibold">{formatDate(bid.created_at)}</p>
                                                    </div>
                                                </div>
                                                <div>
                                                    <p className="mb-1 text-sm text-muted-foreground">Proposal</p>
                                                    <p className="text-sm">{bid.proposal}</p>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Action buttons - only show for pending bids */}
                                        {bid.status === 'pending' && (
                                            <div className="mt-4 flex gap-2 border-t pt-4">
                                                <Button size="sm" onClick={() => handleAcceptBid(bid.id)} className="bg-green-600 hover:bg-green-700">
                                                    Accept Proposal
                                                </Button>
                                                <Button size="sm" variant="outline" onClick={() => handleRejectBid(bid.id)}>
                                                    Reject
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Assigned Freelancer Info - Show when project is assigned */}
                {project.status === 'in_progress' && project.assigned_freelancer && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Assigned Freelancer</CardTitle>
                            <CardDescription>This project has been assigned to a freelancer</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    <User className="h-8 w-8 text-muted-foreground" />
                                    <div>
                                        <p className="font-medium">{project.assigned_freelancer.name}</p>
                                        <p className="text-sm text-muted-foreground">{project.assigned_freelancer.email}</p>
                                    </div>
                                </div>
                                <div className="text-right">
                                    <p className="text-sm text-muted-foreground">Accepted Amount</p>
                                    <p className="font-semibold">₵{Number(project.accepted_bid_amount || 0).toFixed(2)}</p>
                                </div>
                            </div>

                            {/* Milestone Progress Link */}
                            <div className="mt-4 border-t pt-4">
                                <Link
                                    href={route('milestones.index', project.slug)}
                                    className="inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
                                >
                                    <FileText className="mr-2 h-4 w-4" />
                                    View Chapter Milestones
                                </Link>
                                <p className="mt-2 text-sm text-muted-foreground">Track progress and manage chapter deliveries</p>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Project Info */}
                <Card>
                    <CardContent className="pt-6">
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                            <span>Project posted on {formatDate(project.created_at)}</span>
                            <span>Project ID: #{project.id}</span>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
