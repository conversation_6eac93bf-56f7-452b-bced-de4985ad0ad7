import { DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { UserInfo } from '@/components/user-info';
import { useMobileNavigation } from '@/hooks/use-mobile-navigation';
import { type User } from '@/types';
import { Link, router } from '@inertiajs/react';
import { BarChart3, CreditCard, Eye, History, LogOut, PlusCircle, Settings, Shield, User as UserIcon, Users, Wallet } from 'lucide-react';

interface UserMenuContentProps {
    user: User;
}

export function UserMenuContent({ user }: UserMenuContentProps) {
    const cleanup = useMobileNavigation();

    const handleLogout = () => {
        cleanup();
        router.flushAll();
    };

    return (
        <>
            <DropdownMenuLabel className="p-0 font-normal">
                <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <UserInfo user={user} showEmail={true} />
                </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
                <DropdownMenuItem asChild>
                    <Link className="block w-full" href="/profile" prefetch onClick={cleanup}>
                        <UserIcon className="mr-2 h-4 w-4" />
                        View profile
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                    <Link className="block w-full" href="/membership" prefetch onClick={cleanup}>
                        <Users className="mr-2 h-4 w-4" />
                        Membership
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                    <Link className="block w-full" href="/analytics" prefetch onClick={cleanup}>
                        <BarChart3 className="mr-2 h-4 w-4" />
                        Account analytics
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                    <Link className="block w-full" href="/insights" prefetch onClick={cleanup}>
                        <Eye className="mr-2 h-4 w-4" />
                        Bid Insights
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                    <Link className="block w-full" href={route('profile.edit')} as="button" prefetch onClick={cleanup}>
                        <Settings className="mr-2 h-4 w-4" />
                        Settings
                    </Link>
                </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">Finances</DropdownMenuLabel>
            <DropdownMenuGroup>
                <DropdownMenuItem asChild>
                    <Link className="block w-full" href="/balance" prefetch onClick={cleanup}>
                        <Wallet className="mr-2 h-4 w-4" />
                        Balance
                        <span className="ml-auto text-sm text-muted-foreground">₵{Number(user.wallet_balance || 0).toFixed(2)}</span>
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                    <Link className="block w-full" href={route('wallet.add-funds')} prefetch onClick={cleanup}>
                        <PlusCircle className="mr-2 h-4 w-4" />
                        Add funds
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                    <Link className="block w-full" href="/funds/withdraw" prefetch onClick={cleanup}>
                        <CreditCard className="mr-2 h-4 w-4" />
                        Withdraw funds
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                    <Link className="block w-full" href={route('wallet.transactions')} prefetch onClick={cleanup}>
                        <History className="mr-2 h-4 w-4" />
                        Transaction history
                    </Link>
                </DropdownMenuItem>
            </DropdownMenuGroup>

            {/* Admin Section - Only for super admin */}
            {user.role === 'super_admin' && (
                <>
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">Administration</DropdownMenuLabel>
                    <DropdownMenuGroup>
                        <DropdownMenuItem asChild>
                            <Link className="block w-full" href={route('admin.users.index')} prefetch onClick={cleanup}>
                                <Shield className="mr-2 h-4 w-4" />
                                User Management
                            </Link>
                        </DropdownMenuItem>
                    </DropdownMenuGroup>
                </>
            )}

            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
                <Link className="block w-full" method="post" href={route('logout')} as="button" onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Log out
                </Link>
            </DropdownMenuItem>
        </>
    );
}
